/**
 * Mega Menu JavaScript for DmrThema
 * Cok seviyeli mega menu destegiyle gelismis fare kontrolu
 */

document.addEventListener('DOMContentLoaded', function() {

    // Mega menu ve normal menu elementlerini sec
    const megaMenuItems = document.querySelectorAll('.main-navigation ul li.has-mega-menu');
    const normalMenuItems = document.querySelectorAll('.main-navigation ul li:not(.has-mega-menu)');

    // Mega menu islemleri
    if (megaMenuItems.length > 0) {
        initMegaMenus();
    }

    // Normal menu islemleri
    if (normalMenuItems.length > 0) {
        initNormalMenus();
    }

    function initMegaMenus() {
        megaMenuItems.forEach(function(menuItem) {
            const megaMenu = menuItem.querySelector('.mega-menu-container');

            if (!megaMenu) {
                return;
            }

            let hoverTimeout;

            // Ana menu item uzerine fare geldiginde
            menuItem.addEventListener('mouseenter', function() {
                clearTimeout(hoverTimeout);
                closeMegaMenus();

                // Mega menuyu ac
                requestAnimationFrame(function() {
                    menuItem.classList.add('mega-menu-active');
                });
            });

            // Ana menu item'dan fare ciktiginda
            menuItem.addEventListener('mouseleave', function() {
                hoverTimeout = setTimeout(function() {
                    menuItem.classList.remove('mega-menu-active');
                }, 200);
            });

            // Mega menu uzerine fare geldiginde
            megaMenu.addEventListener('mouseenter', function() {
                clearTimeout(hoverTimeout);
                menuItem.classList.add('mega-menu-active');
            });

            // Mega menu'dan fare ciktiginda
            megaMenu.addEventListener('mouseleave', function() {
                menuItem.classList.remove('mega-menu-active');
            });

            // Alt kategoriler icin hover efektleri
            const megaColumns = megaMenu.querySelectorAll('.mega-menu-column');
            megaColumns.forEach(function(column) {
                const columnLink = column.querySelector('a');
                const subMenu = column.querySelector('.mega-sub-menu');

                if (columnLink && subMenu) {
                    // Ana kategori linkine hover
                    columnLink.addEventListener('mouseenter', function() {
                        // Diger sutunlardaki aktif durumu kaldir
                        megaColumns.forEach(function(otherColumn) {
                            otherColumn.classList.remove('column-active');
                        });

                        // Bu sutunu aktif yap
                        column.classList.add('column-active');
                    });
                }
            });
        });
    }

    function initNormalMenus() {
        normalMenuItems.forEach(function(menuItem) {
            const subMenu = menuItem.querySelector('.sub-menu');

            if (!subMenu) {
                return;
            }

            let hoverTimeout;

            menuItem.addEventListener('mouseenter', function() {
                clearTimeout(hoverTimeout);
                closeMegaMenus(); // Mega menuleri kapat

                requestAnimationFrame(function() {
                    menuItem.classList.add('mega-menu-active');
                });
            });

            menuItem.addEventListener('mouseleave', function() {
                hoverTimeout = setTimeout(function() {
                    menuItem.classList.remove('mega-menu-active');
                }, 150);
            });

            subMenu.addEventListener('mouseenter', function() {
                clearTimeout(hoverTimeout);
                menuItem.classList.add('mega-menu-active');
            });

            subMenu.addEventListener('mouseleave', function() {
                menuItem.classList.remove('mega-menu-active');
            });
        });
    }

    // Tum menuleri kapat
    function closeMegaMenus() {
        const allMenuItems = document.querySelectorAll('.main-navigation ul li');
        allMenuItems.forEach(function(item) {
            item.classList.remove('mega-menu-active');
        });

        // Aktif sutunlari temizle
        const activeColumns = document.querySelectorAll('.mega-menu-column.column-active');
        activeColumns.forEach(function(column) {
            column.classList.remove('column-active');
        });
    }

    // Sayfa uzerinde baska yere tiklandiginda menuleri kapat
    document.addEventListener('click', function(e) {
        const navigation = document.querySelector('.main-navigation');
        if (navigation && !navigation.contains(e.target)) {
            closeMegaMenus();
        }
    });

    // ESC tusuna basildiginda menuleri kapat
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeMegaMenus();
        }
    });

    // Mobil cihazlarda dokunma desteği
    if ('ontouchstart' in window) {
        megaMenuItems.forEach(function(menuItem) {
            const mainLink = menuItem.querySelector('a');

            mainLink.addEventListener('touchstart', function(e) {
                if (!menuItem.classList.contains('mega-menu-active')) {
                    e.preventDefault();
                    closeMegaMenus();
                    menuItem.classList.add('mega-menu-active');
                }
            });
        });
    }

});
